<?php

namespace App\Exports;

use App\Models\ConfirmedAffixed;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class ConfirmedAffixReportExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $filters;

    public function __construct($filters = [])
    {
        $this->filters = $filters;
    }

    public function collection()
    {
        $query = ConfirmedAffixed::with([
            'device',
            'route',
            'longRoute',
            'allocationPoint'
        ]);

        // Apply filters
        if (!empty($this->filters['start_date'])) {
            $startDate = $this->filters['start_date'];
            if (!empty($this->filters['start_time'])) {
                $startDate .= ' ' . $this->filters['start_time'];
            }
            $query->where('affixing_date', '>=', $startDate);
        } elseif (!empty($this->filters['start_time'])) {
            $query->whereTime('affixing_date', '>=', $this->filters['start_time']);
        }

        if (!empty($this->filters['end_date'])) {
            $endDate = $this->filters['end_date'];
            if (!empty($this->filters['end_time'])) {
                $endDate .= ' ' . $this->filters['end_time'];
            } else {
                $endDate .= ' 23:59:59';
            }
            $query->where('affixing_date', '<=', $endDate);
        } elseif (!empty($this->filters['end_time'])) {
            $query->whereTime('affixing_date', '<=', $this->filters['end_time']);
        }

        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['device_id'])) {
            $deviceId = $this->filters['device_id'];
            $query->whereHas('device', function($q) use ($deviceId) {
                $q->where('device_id', 'LIKE', "%{$deviceId}%");
            });
        }

        if (!empty($this->filters['boe'])) {
            $query->where('boe', 'LIKE', "%{$this->filters['boe']}%");
        }

        if (!empty($this->filters['vehicle_number'])) {
            $query->where('vehicle_number', 'LIKE', "%{$this->filters['vehicle_number']}%");
        }

        if (!empty($this->filters['destination'])) {
            $query->where('destination', 'LIKE', "%{$this->filters['destination']}%");
        }

        return $query->latest('affixing_date')->get();
    }

    public function headings(): array
    {
        return [
            'Device ID',
            'SAD/T1',
            'Vehicle Number',
            'Destination',
            'Regime',
            'Route',
            'Long Route',
            'Agency',
            'Agent Contact',
            'Truck Number',
            'Driver Name',
            'Affixing Date',
            'Status',
            'Allocation Point'
        ];
    }

    public function map($row): array
    {
        return [
            $row->device->device_id ?? 'N/A',
            $row->boe,
            $row->vehicle_number,
            $row->destination,
            $row->regime,
            $row->route->name ?? 'N/A',
            $row->longRoute->name ?? 'N/A',
            $row->agency,
            $row->agent_contact,
            $row->truck_number,
            $row->driver_name,
            $row->affixing_date ? $row->affixing_date->format('Y-m-d H:i:s') : 'N/A',
            ucfirst(strtolower($row->status)),
            $row->allocationPoint->name ?? 'N/A',
        ];
    }
}
