<?php

namespace App\Filament\Resources\ConfirmedAffixedResource\Pages;

use App\Filament\Resources\ConfirmedAffixedResource;
use App\Models\AllocationPoint;
use App\Models\ConfirmedAffixed;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Route;

class ConfirmedAffixReport extends Page implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;

    protected static string $resource = ConfirmedAffixedResource::class;
    protected static string $view = 'filament.resources.confirmed-affixed-resource.pages.confirmed-affix-report';
    
    protected static ?string $title = 'Confirmed Affix Report';
    protected static bool $shouldRegisterNavigation = false;
    
    public ?array $filters = [];

    public function mount(): void
    {
        $this->filters = [
            'start_date' => request('start_date', now()->subDays(30)->format('Y-m-d')),
            'end_date' => request('end_date', now()->format('Y-m-d')),
            'allocation_point_id' => request('allocation_point_id'),
            'status' => request('status', ''),
            'search' => request('search', ''),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('device.device_id')
                    ->label('Device ID')
                    ->searchable(),
                TextColumn::make('boe')
                    ->label('BOE/SAD')
                    ->searchable(),
                TextColumn::make('vehicle_number')
                    ->label('Vehicle Number')
                    ->searchable(),
                TextColumn::make('allocationPoint.name')
                    ->label('Allocation Point')
                    ->searchable(),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'confirmed' => 'success',
                        'pending' => 'warning',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                // Add any additional filters here
            ]);
    }

    protected function getTableQuery(): Builder
    {
        return ConfirmedAffixed::query()
            ->with(['device', 'allocationPoint'])
            ->when(
                $this->filters['start_date'] ?? null,
                fn (Builder $query, $date) => $query->whereDate('created_at', '>=', $date)
            )
            ->when(
                $this->filters['end_date'] ?? null,
                fn (Builder $query, $date) => $query->whereDate('created_at', '<=', $date)
            )
            ->when(
                $this->filters['allocation_point_id'] ?? null,
                fn (Builder $query, $id) => $query->where('allocation_point_id', $id)
            )
            ->when(
                $this->filters['status'] ?? null,
                fn (Builder $query, $status) => $query->where('status', $status)
            )
            ->when(
                $this->filters['search'] ?? null,
                fn (Builder $query, $search) => $query->where(function($q) use ($search) {
                    $q->whereHas('device', function($q) use ($search) {
                        $q->where('device_id', 'like', "%{$search}%");
                    })
                    ->orWhere('boe', 'like', "%{$search}%")
                    ->orWhere('vehicle_number', 'like', "%{$search}%");
                })
            )
            ->latest();
    }
    
    public function resetFilters(): void
    {
        $this->filters = [
            'start_date' => now()->subDays(30)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'allocation_point_id' => null,
            'status' => '',
            'search' => ''
        ];
        $this->dispatch('refreshTable');
    }
    

}
