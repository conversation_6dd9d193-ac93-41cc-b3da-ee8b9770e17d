<?php

namespace App\Traits;

use Carbon\Carbon;

trait CalculatesOverdueHours
{
    public function calculateOverdueHours(): int
    {
        if (!$this->affixing_date) {
            return 0;
        }

        $affixingDate = Carbon::parse($this->affixing_date);
        $currentDate = Carbon::now();

        // Determine grace period based on route type
        $gracePeriod = ($this->route_id && !$this->long_route_id) ? 24 : 48; // 24 hours for normal routes, 48 for long/no routes

        $hoursDifference = $currentDate->diffInHours($affixingDate);

        // If within grace period, return 0
        if ($hoursDifference <= $gracePeriod) {
            return 0;
        }

        // Return hours exceeding grace period
        return $hoursDifference - $gracePeriod;
    }

    public function updateOverdueHours(): void
    {
        $this->overdue_hours = $this->calculateOverdueHours();
        $this->save();
    }

    // Add new method for calculating overstay days
    public function calculateOverstayDays(): int
    {
        if (!$this->affixing_date) {
            return 0;
        }

        $affixingDate = Carbon::parse($this->affixing_date);
        $currentDate = Carbon::now();

        // Determine grace period based on route type (in days)
        $gracePeriod = ($this->long_route_id) ? 2 : 1; // 2 days for long route, 1 for normal

        $daysDiff = $currentDate->startOfDay()->diffInDays($affixingDate->startOfDay());

        // Return days exceeding grace period
        return max(0, $daysDiff - $gracePeriod);
    }

    public function updateOverstayDays(): void
    {
        $this->overstay_days = $this->calculateOverstayDays();
        $this->save();
    }
}

