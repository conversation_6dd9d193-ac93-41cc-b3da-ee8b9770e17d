<?php

namespace App\Http\Controllers;

use App\Exports\DispatchReportExport;
use App\Models\DataEntryAssignment;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Illuminate\Http\Request;

class DispatchReportController extends Controller
{
    public function export(DataEntryAssignment $assignment, Request $request): BinaryFileResponse
    {
        $filters = [
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
            'allocation_point_id' => $request->input('allocation_point_id'),
        ];

        return Excel::download(
            new DispatchReportExport($assignment->id, $filters),
            "dispatch-report-{$assignment->id}-" . now()->format('Y-m-d') . '.xlsx'
        );
    }
}
