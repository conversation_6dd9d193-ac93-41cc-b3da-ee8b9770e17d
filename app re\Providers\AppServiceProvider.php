<?php

namespace App\Providers;

use App\Models\Device;
use App\Models\DeviceRetrieval;
use App\Models\Invoice;
use App\Observers\DeviceObserver;
use App\Observers\DeviceRetrievalOverstayObserver;
use App\Observers\DeviceRetrievalStatusSyncObserver;
use App\Observers\InvoiceObserver;
use App\Observers\MonitoringOverstayObserver;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Device::observe(DeviceObserver::class);
        DeviceRetrieval::observe(DeviceRetrievalStatusSyncObserver::class);
        Invoice::observe(InvoiceObserver::class);
        \App\Models\Monitoring::observe(\App\Observers\MonitoringOverstayObserver::class);
    }
}




