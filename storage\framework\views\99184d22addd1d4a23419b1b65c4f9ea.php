<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-lg font-medium"><?php echo e($this->getTitle()); ?></h2>
            </div>
        </div>

        <div class="bg-white p-4 rounded-lg shadow">
            <div class="flex flex-wrap gap-4">
                <!--[if BLOCK]><![endif]--><?php if(!$showAssignedToAgent): ?>
                <button wire:click="filterByStatus('ONLINE')" 
                    class="px-4 py-2 text-sm rounded-lg transition-colors duration-200
                    <?php echo e(isset($this->tableFilters['status']['values']) && in_array('ONLINE', $this->tableFilters['status']['values']) 
                        ? 'bg-success-500 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                    Online
                </button>
                <button wire:click="filterByStatus('OFFLINE')" 
                    class="px-4 py-2 text-sm rounded-lg transition-colors duration-200
                    <?php echo e(isset($this->tableFilters['status']['values']) && in_array('OFFLINE', $this->tableFilters['status']['values']) 
                        ? 'bg-danger-500 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                    Offline
                </button>
                <button wire:click="filterByStatus('DAMAGED')" 
                    class="px-4 py-2 text-sm rounded-lg transition-colors duration-200
                    <?php echo e(isset($this->tableFilters['status']['values']) && in_array('DAMAGED', $this->tableFilters['status']['values']) 
                        ? 'bg-warning-500 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                    Damaged
                </button>
                <button wire:click="filterByStatus('FIXED')" 
                    class="px-4 py-2 text-sm rounded-lg transition-colors duration-200
                    <?php echo e(isset($this->tableFilters['status']['values']) && in_array('FIXED', $this->tableFilters['status']['values']) 
                        ? 'bg-info-500 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                    Fixed
                </button>
                <button wire:click="filterByStatus('LOST')" 
                    class="px-4 py-2 text-sm rounded-lg transition-colors duration-200
                    <?php echo e(isset($this->tableFilters['status']['values']) && in_array('LOST', $this->tableFilters['status']['values']) 
                        ? 'bg-gray-500 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                    Lost
                </button>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                <button wire:click="filterByStatus('ASSIGNED TO AGENT')" 
                    class="px-4 py-2 text-sm rounded-lg transition-colors duration-200
                    <?php echo e($showAssignedToAgent
                        ? 'bg-primary-500 text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                    <?php echo e($showAssignedToAgent ? 'Show Assigned Devices' : 'Assigned to Agent'); ?>

                </button>
            </div>
        </div>

        <div class="flex justify-end mb-4">
            <!--[if BLOCK]><![endif]--><?php if(!$showAssignedToAgent): ?>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getHeaderActions(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php echo e($action); ?>

                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>

<?php echo e($this->table); ?>



</div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?><?php /**PATH C:\laragon\www\Crowdfunding1\inventory system 3\resources\views/filament/resources/data-entry-assignment/pages/view-assignment-data-entry.blade.php ENDPATH**/ ?>