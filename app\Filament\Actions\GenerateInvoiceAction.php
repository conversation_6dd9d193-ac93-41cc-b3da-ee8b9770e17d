<?php

namespace App\Filament\Actions;

use App\Models\DeviceRetrieval;
use App\Models\Invoice;
use Filament\Tables\Actions\Action;
use Filament\Forms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GenerateInvoiceAction extends Action
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Generate Invoice')
            ->icon('heroicon-o-document-text')
            ->color('success')
            ->form(function ($record) {
                return [
                    Forms\Components\Section::make('Invoice Details')
                        ->schema([
                            Forms\Components\TextInput::make('reference_number')
                                ->label('Reference Number')
                                ->default('INV-' . now()->format('YmdHis'))
                                ->required()
                                ->maxLength(255),

                            Forms\Components\TextInput::make('sad_boe')
                                ->label('SAD/BOE Number')
                                ->default($record->sad_number ?? $record->boe ?? 'N/A')
                                ->required(),

                            Forms\Components\TextInput::make('regime')
                                ->label('Regime')
                                ->default($record->regime ?? 'N/A')
                                ->required(),

                            Forms\Components\TextInput::make('agent')
                                ->label('Agent')
                                ->default($record->agency ?? $record->agent_contact ?? 'N/A')
                                ->required(),
                        ])
                        ->columns(2),

                    Forms\Components\Section::make('Device & Route Information')
                        ->schema([
                            Forms\Components\TextInput::make('device_number')
                                ->label('Device Number')
                                ->default($record->device?->device_id ?? 'N/A')
                                ->required(),

                            Forms\Components\TextInput::make('route')
                                ->label('Route')
                                ->default($this->getRouteValue($record))
                                ->required(),

                            Forms\Components\TextInput::make('departure')
                                ->label('Departure')
                                ->default('BANJUL')
                                ->required(),

                            Forms\Components\TextInput::make('destination')
                                ->label('Destination')
                                ->default($record->destination ?? 'N/A')
                                ->required(),
                        ])
                        ->columns(2),

                    Forms\Components\Section::make('Penalty Calculation')
                        ->schema([
                            Forms\Components\TextInput::make('overstay_days')
                                ->label('Overstay Days')
                                ->default($record->overstay_days ?? 0)
                                ->numeric()
                                ->required()
                                ->reactive()
                                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                                    $set('total_amount', $state * $get('penalty_amount'))
                                ),

                            Forms\Components\TextInput::make('penalty_amount')
                                ->label('Penalty Amount (Per Day)')
                                ->default(1000)
                                ->numeric()
                                ->prefix('GMD')
                                ->required()
                                ->reactive()
                                ->afterStateUpdated(fn ($state, callable $set, callable $get) =>
                                    $set('total_amount', $get('overstay_days') * $state)
                                ),

                            Forms\Components\TextInput::make('total_amount')
                                ->label('Total Amount')
                                ->default(($record->overstay_days ?? 0) * 1000)
                                ->numeric()
                                ->prefix('GMD')
                                ->required()
                                ->disabled(),
                        ])
                        ->columns(3),

                    Forms\Components\Section::make('Payment Information')
                        ->schema([
                            Forms\Components\Textarea::make('description')
                                ->label('Description')
                                ->default('Penalty payment for overstayed trip')
                                ->required(),

                            Forms\Components\TextInput::make('paid_by')
                                ->label('Paid By')
                                ->default(auth()->user()->name)
                                ->required(),

                            Forms\Components\TextInput::make('received_by')
                                ->label('Received By')
                                ->default('Finance Department')
                                ->required(),

                            Forms\Components\FileUpload::make('logo')
                                ->label('Invoice Logo')
                                ->image()
                                ->directory('invoice_logos')
                                ->visibility('private'),
                        ])
                        ->columns(2),
                ];
            })
            ->action(function (array $data, $record) {
                try {
                    DB::beginTransaction();

                    // Handle signature upload
                    $signaturePath = null;
                    if (isset($data['signature']) && !empty($data['signature'])) {
                        $signaturePath = $data['signature'];
                    }

                    // Calculate total amount
                    $totalAmount = ($data['overstay_days'] ?? $record->overstay_days ?? 0) * ($data['penalty_amount'] ?? 1000);

                    // Debug: Log the form data to help identify missing fields
                    \Log::info('Invoice creation data:', $data);

                    // Create invoice with validated form data
                    $invoice = Invoice::create([
                        'device_retrieval_id' => $record->id,
                        'reference_number' => $data['reference_number'],
                        'reference_date' => now(),
                        'sad_boe' => $data['sad_boe'],
                        'regime' => $data['regime'],
                        'agent' => $data['agent'],
                        'route' => $data['route'],
                        'overstay_days' => $data['overstay_days'],
                        'penalty_amount' => $data['penalty_amount'],
                        'device_number' => $data['device_number'],
                        'total_amount' => $data['total_amount'],
                        'description' => $data['description'],
                        'paid_by' => $data['paid_by'],
                        'received_by' => $data['received_by'],
                        'logo_path' => isset($data['logo']) && !empty($data['logo']) ? $data['logo'][0] : null,
                        'status' => 'PP', // Pending Payment
                    ]);

                    // Update device retrieval payment status if not already paid
                    if ($record->payment_status !== 'PD') {
                        $record->update([
                            'payment_status' => 'PD',
                            'finance_approval_date' => now(),
                            'finance_approved_by' => auth()->id(),
                        ]);
                    }

                    DB::commit();

                    // Send notification to finance officers
                    $invoice->sendPendingApprovalNotification();

                    // Generate PDF and provide download link
                    $url = route('invoices.download', $invoice->id);

                    Notification::make()
                        ->title('Invoice generated successfully')
                        ->success()
                        ->send();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Invoice generation failed', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'device_retrieval_id' => $record->id
                    ]);

                    Notification::make()
                        ->danger()
                        ->title('Error')
                        ->body('Failed to generate invoice: ' . $e->getMessage())
                        ->send();
                }
            })
            ->modalWidth('4xl')
            ->modalHeading('Generate Overstay Penalty Invoice')
            ->visible(function (DeviceRetrieval $record): bool {
                return $record->overstay_days >= 2;
            });
    }

    /**
     * Get route value with proper fallbacks
     */
    private function getRouteValue($record): string
    {
        // Try different possible route sources
        if ($record->route?->name) {
            return $record->route->name;
        }

        if ($record->longRoute?->name) {
            return $record->longRoute->name;
        }

        if (isset($record->route_name)) {
            return $record->route_name;
        }

        if (isset($record->destination)) {
            return "Route to " . $record->destination;
        }

        return 'Unknown Route';
    }
}