<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\DeviceRetrieval;

class LiveCurrentTimeUpdater extends Component
{
    public $deviceRetrievalId;
    public $currentTime;
    public $formattedTime;

    protected $listeners = ['refreshCurrentTime' => '$refresh'];

    public function mount($deviceRetrievalId)
    {
        $this->deviceRetrievalId = $deviceRetrievalId;
        $this->updateCurrentTime();
    }

    public function updateCurrentTime()
    {
        $deviceRetrieval = DeviceRetrieval::find($this->deviceRetrievalId);
        if ($deviceRetrieval) {
            $this->currentTime = $deviceRetrieval->current_time;
            $this->formattedTime = $this->currentTime ? $this->currentTime->diffForHumans() : 'N/A';
        }
    }

    public function render()
    {
        return view('livewire.live-current-time-updater');
    }
}
