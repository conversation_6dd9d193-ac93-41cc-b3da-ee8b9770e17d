<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('confirmed_affix_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('device_id')->constrained('devices')->cascadeOnDelete();
            $table->foreignId('confirmed_affixed_id')->constrained('confirmed_affixeds')->cascadeOnDelete();
            $table->foreignId('allocation_point_id')->nullable()->constrained('allocation_points')->nullOnDelete();
            $table->foreignId('picked_by')->constrained('users')->cascadeOnDelete();
            $table->timestamp('picked_at');
            $table->date('picked_date');
            $table->time('picked_time');
            $table->json('details')->nullable(); // Store affixing details
            $table->timestamps();
            
            // Indexes for better query performance
            $table->index(['device_id', 'picked_at']);
            $table->index(['confirmed_affixed_id']);
            $table->index(['allocation_point_id']);
            $table->index(['picked_by']);
            $table->index(['picked_date']);
            $table->index(['picked_time']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('confirmed_affix_logs');
    }
};
