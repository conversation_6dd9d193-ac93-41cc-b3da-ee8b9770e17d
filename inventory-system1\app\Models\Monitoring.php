<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\HasNotifications;
use App\Traits\CalculatesOverdueHours;
use Illuminate\Support\Facades\DB;
use App\Models\Device;
use App\Models\Route;
use App\Models\LongRoute;
use App\Models\DeviceRetrieval;

class Monitoring extends Model
{
    use HasFactory, CalculatesOverdueHours, HasNotifications;

    protected $table = 'monitorings';

    protected $fillable = [
        'date',
        'current_date',
        'device_id',
        'boe',
        'sad_number',
        'vehicle_number',
        'regime',
        'destination',
        'route_id',
        'long_route_id',
        'manifest_date',
        'note',
        'agency',
        'agent_contact',
        'truck_number',
        'driver_name',
        'affixing_date',
        'status',
        'overstay_days',
        'retrieval_status'
    ];



    protected $casts = [
        'date' => 'datetime',
        'current_date' => 'datetime',
        'manifest_date' => 'date',
        'affixing_date' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($monitoring) {
            // Check for existing record with same device_id
            $existing = static::where('device_id', $monitoring->device_id)
                ->where('id', '!=', $monitoring->id)
                ->first();

            if ($existing) {
                // Delete the existing record
                $existing->delete();
            }
        });
    }

    public function addNewNote(string $note, ?string $manifestDate = null): bool
    {
        try {
            // Using Query Builder for direct database access
            DB::table($this->table)
                ->where('id', $this->id)
                ->update([
                    'note' => $note,
                    'manifest_date' => $manifestDate,
                    'updated_at' => now()
                ]);

            // Refresh the model to get the new data
            $this->refresh();

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to add note to monitoring: ' . $e->getMessage());
            return false;
        }
    }

    public function device(): BelongsTo
    {
        return $this->belongsTo(Device::class, 'device_id', 'id');
    }

    public function deviceRetrievals()
    {
        return $this->hasMany(DeviceRetrieval::class, 'device_id', 'device_id')
            ->whereIn('retrieval_status', ['RETRIEVED', 'NOT_RETRIEVED'])
            ->latest();
    }

    /**
     * Get the latest device retrieval for this monitoring record
     */
    public function latestDeviceRetrieval()
    {
        return $this->hasOne(DeviceRetrieval::class, 'device_id', 'device_id')
            ->latest();
    }

    public function route(): BelongsTo
    {
        return $this->belongsTo(Route::class);
    }

    public function longRoute(): BelongsTo
    {
        return $this->belongsTo(LongRoute::class);
    }
}


