<?php

namespace App\Observers;

use App\Models\DeviceRetrieval;
use App\Models\Regime;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class DeviceRetrievalOverstayObserver
{
    /**
     * Handle the DeviceRetrieval "creating" event.
     */
    public function creating(DeviceRetrieval $deviceRetrieval): void
    {
        // Set default retrieval status if not set
        if (!isset($deviceRetrieval->retrieval_status)) {
            $deviceRetrieval->retrieval_status = 'NOT_RETRIEVED';
        }

        // Set destination based on regime
        $regime = Regime::find($deviceRetrieval->regime);
        if ($regime) {
            switch (strtolower($regime->name)) {
                case 'warehouse':
                    $deviceRetrieval->destination = 'Ghana';
                    break;
                case 'transit':
                    $deviceRetrieval->destination = 'Soma';
                    break;
                default:
                    $deviceRetrieval->destination = 'Unknown';
                    break;
            }
        }

        Log::info('DeviceRetrieval creating', [
            'device_id' => $deviceRetrieval->device_id,
            'regime' => $deviceRetrieval->regime,
            'destination' => $deviceRetrieval->destination,
        ]);

        try {
            Log::info('DeviceRetrievalOverstayObserver: Creating device retrieval', [
                'device_id' => $deviceRetrieval->device_id,
                'date' => $deviceRetrieval->date,
                'affixing_date' => $deviceRetrieval->affixing_date,
                'manifest_date' => $deviceRetrieval->manifest_date,
                'long_route_id' => $deviceRetrieval->long_route_id
            ]);

            $this->calculateAndUpdateOverstay($deviceRetrieval);

            Log::info('DeviceRetrievalOverstayObserver: Successfully processed creating event', [
                'device_id' => $deviceRetrieval->device_id,
                'calculated_overstay_days' => $deviceRetrieval->overstay_days,
                'calculated_overstay_amount' => $deviceRetrieval->overstay_amount
            ]);

        } catch (\Exception $e) {
            Log::error('DeviceRetrievalOverstayObserver: Error in creating event', [
                'device_id' => $deviceRetrieval->device_id,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString(),
                'device_retrieval_data' => [
                    'date' => $deviceRetrieval->date,
                    'affixing_date' => $deviceRetrieval->affixing_date,
                    'manifest_date' => $deviceRetrieval->manifest_date,
                    'long_route_id' => $deviceRetrieval->long_route_id,
                    'boe' => $deviceRetrieval->boe ?? 'N/A'
                ]
            ]);

            // Set safe defaults on error
            $deviceRetrieval->overstay_days = 0;
            $deviceRetrieval->overstay_amount = 0.00;
        }
    }

    /**
     * Handle the DeviceRetrieval "created" event.
     */
    public function created(DeviceRetrieval $deviceRetrieval): void
    {
        try {
            Log::info('DeviceRetrievalOverstayObserver: Device retrieval created', [
                'id' => $deviceRetrieval->id,
                'device_id' => $deviceRetrieval->device_id,
                'boe' => $deviceRetrieval->boe,
                'overstay_days' => $deviceRetrieval->overstay_days,
                'overstay_amount' => $deviceRetrieval->overstay_amount,
                'reference_date_used' => $this->determineReferenceDate($deviceRetrieval)?->toDateString(),
                'grace_period' => $deviceRetrieval->long_route_id ? 2 : 1
            ]);

        } catch (\Exception $e) {
            Log::error('DeviceRetrievalOverstayObserver: Error in created event', [
                'id' => $deviceRetrieval->id ?? 'unknown',
                'device_id' => $deviceRetrieval->device_id ?? 'unknown',
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle the DeviceRetrieval "updating" event.
     */
    public function updating(DeviceRetrieval $deviceRetrieval): void
    {
        $relevantFields = ['date', 'affixing_date', 'long_route_id', 'manifest_date'];
        $changedFields = array_keys($deviceRetrieval->getDirty());
        $hasRelevantChanges = !empty(array_intersect($relevantFields, $changedFields));

        Log::info('DeviceRetrievalOverstayObserver: Updating device retrieval', [
            'id' => $deviceRetrieval->id,
            'device_id' => $deviceRetrieval->device_id,
            'changed_fields' => $changedFields,
            'relevant_fields_changed' => $hasRelevantChanges,
            'old_date' => $deviceRetrieval->getOriginal('date'),
            'new_date' => $deviceRetrieval->date,
            'old_affixing_date' => $deviceRetrieval->getOriginal('affixing_date'),
            'new_affixing_date' => $deviceRetrieval->affixing_date,
            'old_overstay_days' => $deviceRetrieval->getOriginal('overstay_days'),
            'old_overstay_amount' => $deviceRetrieval->getOriginal('overstay_amount'),
            'model_dirty' => $deviceRetrieval->isDirty(),
            'model_was_changed' => $deviceRetrieval->wasChanged(),
            'model_original' => $deviceRetrieval->getOriginal()
        ]);

        // Always recalculate to ensure values are up to date
        // This handles cases where the model might be updated through other means
        Log::info('DeviceRetrievalOverstayObserver: Recalculating overstay for device retrieval', [
            'id' => $deviceRetrieval->id,
            'reason' => $hasRelevantChanges ? 'relevant_fields_changed' : 'forced_recalculation',
            'relevant_fields' => $relevantFields,
            'changed_fields' => $changedFields,
            'intersection' => array_intersect($relevantFields, $changedFields)
        ]);

        $this->calculateAndUpdateOverstay($deviceRetrieval);

        // Log the changes that will be saved
        Log::info('DeviceRetrievalOverstayObserver: Changes to be saved', [
            'id' => $deviceRetrieval->id,
            'overstay_days' => $deviceRetrieval->overstay_days,
            'overstay_amount' => $deviceRetrieval->overstay_amount,
            'is_dirty' => $deviceRetrieval->isDirty()
        ]);
    }

    /**
     * Handle the DeviceRetrieval "updated" event.
     */
    public function updated(DeviceRetrieval $deviceRetrieval): void
    {
        // Refresh the model to get the latest values from the database
        $deviceRetrieval->refresh();

        Log::info('DeviceRetrievalOverstayObserver: Device retrieval updated', [
            'id' => $deviceRetrieval->id,
            'device_id' => $deviceRetrieval->device_id,
            'affixing_date' => $deviceRetrieval->affixing_date,
            'date' => $deviceRetrieval->date,
            'final_overstay_days' => $deviceRetrieval->overstay_days,
            'final_overstay_amount' => $deviceRetrieval->overstay_amount,
            'was_recently_created' => $deviceRetrieval->wasRecentlyCreated,
            'was_changed' => $deviceRetrieval->wasChanged(),
            'changes' => $deviceRetrieval->getChanges()
        ]);

        // Verify the values were saved correctly
        $currentRecord = DeviceRetrieval::find($deviceRetrieval->id);
        if ($currentRecord) {
            Log::info('DeviceRetrievalOverstayObserver: Database verification', [
                'id' => $currentRecord->id,
                'db_overstay_days' => $currentRecord->overstay_days,
                'db_overstay_amount' => $currentRecord->overstay_amount,
                'db_updated_at' => $currentRecord->updated_at
            ]);
        }
    }

    /**
     * Calculate and update overstay days and amount
     */
    /**
     * Calculate and update overstay days and amount
     * Uses direct DB queries to ensure data consistency
     */
    private function calculateAndUpdateOverstay(DeviceRetrieval $deviceRetrieval): void
    {
        DB::beginTransaction();

        try {
            // Ensure current_time is set
            if (empty($deviceRetrieval->current_time)) {
                $deviceRetrieval->current_time = now();

                // Only update the database if this is an existing record
                if ($deviceRetrieval->id) {
                    DB::table('device_retrievals')
                        ->where('id', $deviceRetrieval->id)
                        ->update(['current_time' => $deviceRetrieval->current_time]);
                }
            }

            $logContext = [
                'device_retrieval_id' => $deviceRetrieval->id ?? 'creating',
                'device_id' => $deviceRetrieval->device_id,
                'date' => $deviceRetrieval->date?->toDateString(),
                'affixing_date' => $deviceRetrieval->affixing_date?->toDateString(),
                'manifest_date' => $deviceRetrieval->manifest_date?->toDateString(),
                'current_time' => $deviceRetrieval->current_time?->toDateTimeString(),
                'long_route_id' => $deviceRetrieval->long_route_id,
                'calculation_time' => now()->toDateTimeString()
            ];

            Log::info('DeviceRetrievalOverstayObserver: Starting overstay calculation', $logContext);

            // Determine the reference date for calculation
            $referenceDate = $this->determineReferenceDate($deviceRetrieval);

            if (!$referenceDate) {
                Log::warning('DeviceRetrievalOverstayObserver: No valid reference date found', $logContext);

                // Update database directly to ensure consistency
                if ($deviceRetrieval->id) {
                    DB::table('device_retrievals')
                        ->where('id', $deviceRetrieval->id)
                        ->update([
                            'overstay_days' => 0,
                            'overstay_amount' => 0.00,
                            'updated_at' => now()
                        ]);
                }

                // Also update the model in memory
                $deviceRetrieval->overstay_days = 0;
                $deviceRetrieval->overstay_amount = 0.00;

                DB::commit();
                return;
            }

            // Calculate values
            $overdueHours = $this->calculateOverdueHours($referenceDate, $deviceRetrieval);
            $overstayDays = $this->calculateOverstayDays($overdueHours, $deviceRetrieval);
            $overstayAmount = $this->calculateOverstayAmount($overstayDays);

            $updateData = [
                'overstay_days' => $overstayDays,
                'overstay_amount' => $overstayAmount,
                'updated_at' => now()
            ];

            // Log the calculated values
            $calculationDetails = array_merge($logContext, [
                'reference_date' => $referenceDate->toDateString(),
                'current_date' => now()->toDateString(),
                'days_difference' => now()->startOfDay()->diffInDays($referenceDate->startOfDay()),
                'grace_period' => $deviceRetrieval->long_route_id ? 2 : 1,
                'calculated_overstay_days' => $overstayDays,
                'calculated_overstay_amount' => $overstayAmount,
                'old_overstay_days' => $deviceRetrieval->overstay_days,
                'old_overstay_amount' => $deviceRetrieval->overstay_amount
            ]);

            Log::info('DeviceRetrievalOverstayObserver: Overstay calculation completed', $calculationDetails);

            // Update database directly if we have an ID (updating existing record)
            if ($deviceRetrieval->id) {
                $affected = DB::table('device_retrievals')
                    ->where('id', $deviceRetrieval->id)
                    ->update($updateData);

                Log::info('DeviceRetrievalOverstayObserver: Direct database update result', [
                    'device_retrieval_id' => $deviceRetrieval->id,
                    'rows_affected' => $affected,
                    'update_data' => $updateData
                ]);
            }

            // Also update the model in memory
            $deviceRetrieval->overstay_days = $overstayDays;
            $deviceRetrieval->overstay_amount = $overstayAmount;

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();

            $errorContext = array_merge($logContext ?? [], [
                'error' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]
            ]);

            Log::error('DeviceRetrievalOverstayObserver: Error calculating overstay', $errorContext);

            // Set safe defaults on error
            if ($deviceRetrieval->id) {
                try {
                    DB::table('device_retrievals')
                        ->where('id', $deviceRetrieval->id)
                        ->update([
                            'overstay_days' => 0,
                            'overstay_amount' => 0.00,
                            'updated_at' => now()
                        ]);
                } catch (\Exception $dbError) {
                    Log::error('DeviceRetrievalOverstayObserver: Failed to set safe defaults in database', [
                        'device_retrieval_id' => $deviceRetrieval->id,
                        'error' => $dbError->getMessage()
                    ]);
                }
            }

            $deviceRetrieval->overstay_days = 0;
            $deviceRetrieval->overstay_amount = 0.00;
        }
    }

    /**
     * Determine the reference date for overstay calculation
     * Priority: affixing_date > manifest_date > date
     */
    private function determineReferenceDate(DeviceRetrieval $deviceRetrieval): ?Carbon
    {
        $dates = [
            'affixing_date' => $deviceRetrieval->affixing_date,
            'manifest_date' => $deviceRetrieval->manifest_date,
            'date' => $deviceRetrieval->date
        ];

        foreach ($dates as $field => $date) {
            if ($date) {
                $carbonDate = $date instanceof Carbon ? $date : Carbon::parse($date);

                Log::info('DeviceRetrievalOverstayObserver: Using reference date', [
                    'device_retrieval_id' => $deviceRetrieval->id ?? 'creating',
                    'field_used' => $field,
                    'reference_date' => $carbonDate->toDateString()
                ]);

                return $carbonDate;
            }
        }

        return null;
    }

    /**
     * Calculate overdue hours based on reference date and grace period
     */
    private function calculateOverdueHours(Carbon $referenceDate, DeviceRetrieval $deviceRetrieval): int
    {
        try {
            // Use current_time if available, otherwise fall back to now()
            $currentTime = $deviceRetrieval->current_time ?? now();

            if (!$currentTime instanceof Carbon) {
                $currentTime = Carbon::parse($currentTime);
            }

            // Calculate hours difference from reference date to current_time
            $hoursDifference = $currentTime->diffInHours($referenceDate, false);

            // Determine grace period in hours (24h for normal, 48h for long routes)
            $graceHours = $deviceRetrieval->long_route_id ? 48 : 24;

            // Calculate overdue hours (subtract grace period, ensure it's not negative)
            $overdueHours = max(0, $hoursDifference - $graceHours);

            Log::info('DeviceRetrievalOverstayObserver: Overdue hours calculation', [
                'device_retrieval_id' => $deviceRetrieval->id ?? 'creating',
                'reference_date' => $referenceDate->toDateTimeString(),
                'current_time' => $currentTime->toDateTimeString(),
                'hours_difference' => $hoursDifference,
                'grace_hours' => $graceHours,
                'route_type' => $deviceRetrieval->long_route_id ? 'long' : 'normal',
                'calculated_overdue_hours_for_days_calc' => $overdueHours
            ]);

            return $overdueHours;

        } catch (\Exception $e) {
            Log::error('DeviceRetrievalOverstayObserver: Error in calculateOverdueHours', [
                'device_retrieval_id' => $deviceRetrieval->id ?? 'unknown',
                'error' => $e->getMessage(),
                'reference_date' => $referenceDate->toDateTimeString() ?? 'invalid',
                'current_time' => $deviceRetrieval->current_time ?? 'not_set'
            ]);

            // Return 0 to prevent incorrect overstay calculations
            return 0;
        }
    }

    /**
     * Calculate overstay days based on overdue hours
     * Each 24 hours of overstay counts as 1 day
     */
    private function calculateOverstayDays(int $overdueHours, DeviceRetrieval $deviceRetrieval): int
    {
        if ($overdueHours <= 0) {
            return 0;
        }

        // Calculate days (round up to nearest day)
        $overstayDays = (int) ceil($overdueHours / 24);

        Log::info('DeviceRetrievalOverstayObserver: Converted overdue hours to days', [
            'device_retrieval_id' => $deviceRetrieval->id ?? 'creating',
            'overdue_hours_for_calc' => $overdueHours,
            'calculated_overstay_days' => $overstayDays
        ]);

        return $overstayDays;
    }

    /**
     * Calculate overstay amount based on overstay days
     * Business Rule: D1000 per day starting from day 1
     * 1 day = D1000, 3 days = D3000
     */
    /**
     * Calculate overstay amount based on overstay days
     * Business Rule: D1000 per day starting from day 1
     * 1 day = D1000, 3 days = D3000
     */
    private function calculateOverstayAmount(int $overstayDays): float
    {
        if ($overstayDays <= 0) {
            return 0.00;
        }

        // D1000 per day of overstay
        $baseAmount = 1000.00;
        $totalAmount = $baseAmount * $overstayDays;

        // Ensure we don't have negative amounts
        $totalAmount = max(0, $totalAmount);

        Log::info('DeviceRetrievalOverstayObserver: Overstay amount calculation', [
            'overstay_days' => $overstayDays,
            'base_amount_per_day' => $baseAmount,
            'calculated_total_amount' => $totalAmount
        ]);

        return $totalAmount;
    }

    /**
     * Sync overdue days for a device retrieval
     * This uses direct database updates to ensure data consistency
     */
    private function syncOverdueDays(DeviceRetrieval $deviceRetrieval): void
    {
        if (!$deviceRetrieval->id) {
            Log::warning('DeviceRetrievalOverstayObserver: Cannot sync - device retrieval has no ID');
            return;
        }

        DB::beginTransaction();

        try {
            // First, get fresh data from database
            $freshData = DB::table('device_retrievals')
                ->where('id', $deviceRetrieval->id)
                ->first();

            if (!$freshData) {
                throw new \Exception("Device retrieval not found in database");
            }

            // Calculate values using the fresh data
            $referenceDate = $this->determineReferenceDate($deviceRetrieval);

            if (!$referenceDate) {
                throw new \Exception("No valid reference date found for calculation");
            }

            $overdueHours = $this->calculateOverdueHours($referenceDate, $deviceRetrieval);
            $overstayDays = $this->calculateOverstayDays($overdueHours, $deviceRetrieval);
            $overstayAmount = $this->calculateOverstayAmount($overstayDays);

            // Update the database directly
            $updateData = [
                'overstay_days' => $overstayDays,
                'overstay_amount' => $overstayAmount,
                'updated_at' => now()
            ];

            $affected = DB::table('device_retrievals')
                ->where('id', $deviceRetrieval->id)
                ->update($updateData);

            // Update the model in memory
            $deviceRetrieval->fill($updateData);

            DB::commit();

            Log::info('DeviceRetrievalOverstayObserver: Successfully synced overdue days', [
                'device_retrieval_id' => $deviceRetrieval->id,
                'rows_affected' => $affected,
                'update_data' => $updateData
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('DeviceRetrievalOverstayObserver: Error syncing overdue days', [
                'device_retrieval_id' => $deviceRetrieval->id,
                'error' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]
            ]);

            // Set safe defaults on error
            $deviceRetrieval->overstay_days = 0;
            $deviceRetrieval->overstay_amount = 0.00;
        }
    }
}
