<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ConfirmedAffixLog extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'confirmed_affix_logs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'device_id',
        'confirmed_affixed_id',
        'allocation_point_id',
        'picked_by',
        'picked_at',
        'picked_date',
        'picked_time',
        'details',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'picked_at' => 'datetime',
        'picked_date' => 'date',
        'picked_time' => 'datetime:H:i:s',
        'details' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the device that owns the confirmed affix log.
     */
    public function device(): BelongsTo
    {
        return $this->belongsTo(Device::class);
    }

    /**
     * Get the confirmed affixed record that owns the log.
     */
    public function confirmedAffixed(): BelongsTo
    {
        return $this->belongsTo(ConfirmedAffixed::class);
    }

    /**
     * Get the allocation point associated with the log.
     */
    public function allocationPoint(): BelongsTo
    {
        return $this->belongsTo(AllocationPoint::class);
    }

    /**
     * Get the user who picked the device for affixing.
     */
    public function picker(): BelongsTo
    {
        return $this->belongsTo(User::class, 'picked_by');
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('picked_date', [$startDate, $endDate]);
    }

    /**
     * Scope a query to filter by time range.
     */
    public function scopeTimeRange($query, $startTime, $endTime)
    {
        return $query->whereBetween('picked_time', [$startTime, $endTime]);
    }

    /**
     * Scope a query to filter by allocation point.
     */
    public function scopeByAllocationPoint($query, $allocationPointId)
    {
        return $query->where('allocation_point_id', $allocationPointId);
    }

    /**
     * Scope a query to filter by device ID.
     */
    public function scopeByDevice($query, $deviceId)
    {
        return $query->whereHas('device', function ($q) use ($deviceId) {
            $q->where('device_id', 'LIKE', "%{$deviceId}%");
        });
    }

    /**
     * Scope a query to filter by picker (user who affixed).
     */
    public function scopeByPicker($query, $userId)
    {
        return $query->where('picked_by', $userId);
    }

    /**
     * Get formatted picked date and time.
     */
    public function getFormattedPickedAtAttribute(): string
    {
        return $this->picked_at?->format('Y-m-d H:i:s') ?? 'N/A';
    }

    /**
     * Get the BOE number from details.
     */
    public function getBoeAttribute(): ?string
    {
        return $this->details['boe'] ?? null;
    }

    /**
     * Get the vehicle number from details.
     */
    public function getVehicleNumberAttribute(): ?string
    {
        return $this->details['vehicle_number'] ?? null;
    }

    /**
     * Get the destination from details.
     */
    public function getDestinationAttribute(): ?string
    {
        return $this->details['destination'] ?? null;
    }

    /**
     * Get the allocation point name from details.
     */
    public function getAllocationPointNameAttribute(): ?string
    {
        return $this->details['allocation_point_name'] ?? $this->allocationPoint?->name;
    }
}
