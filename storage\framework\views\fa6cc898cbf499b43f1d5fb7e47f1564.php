<div class="space-y-4">
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <h3 class="text-lg font-medium text-gray-900">
                Dispatch Report - <?php echo e($assignment->title ?? 'N/A'); ?>

            </h3>
            <p class="mt-1 text-sm text-gray-500">
                <?php echo e($dispatchLogs->total()); ?> records found
            </p>
        </div>
        
        <!-- Search Filters -->
        <div class="px-6 py-4 border-b border-gray-200">
            <form wire:submit.prevent="$refresh" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Device ID Search -->
                    <div>
                        <label for="device_id" class="block text-sm font-medium text-gray-700 mb-1">Device ID</label>
                        <input type="text" id="device_id" wire:model.live="filters.device_id" 
                               placeholder="Search by device ID"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                    
                    <!-- Date Range Picker -->
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" id="start_date" wire:model.live="filters.start_date" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" id="end_date" wire:model.live="filters.end_date" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <!-- Time Range Picker -->
                    <div class="md:col-span-1">
                        <label for="start_time" class="block text-sm font-medium text-gray-700 mb-1">Time From</label>
                        <input type="time" id="start_time" wire:model.live="filters.start_time" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                    <div class="md:col-span-1">
                        <label for="end_time" class="block text-sm font-medium text-gray-700 mb-1">Time To</label>
                        <input type="time" id="end_time" wire:model.live="filters.end_time" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                    
                    <!-- Allocation Point Filter -->
                    <div class="md:col-span-3">
                        <label for="allocation_point_id" class="block text-sm font-medium text-gray-700 mb-1">Allocation Point</label>
                        <select id="allocation_point_id" wire:model.live="filters.allocation_point_id" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            <option value="">All Allocation Points</option>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $allocationPoints; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $point): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($point->id); ?>"><?php echo e($point->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" wire:click="resetFilters" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Reset Filters
                    </button>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Apply Filters
                    </button>
                </div>
            </form>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dispatched At</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dispatched By</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BOE #</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vehicle #</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $dispatchLogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap"><?php echo e($log->device->device_id ?? 'N/A'); ?></td>
                            <td class="px-6 py-4 whitespace-nowrap"><?php echo e($log->dispatched_at?->format('M d, Y h:i A')); ?></td>
                            <td class="px-6 py-4 whitespace-nowrap"><?php echo e($log->dispatcher->name ?? 'N/A'); ?></td>
                            <td class="px-6 py-4 whitespace-nowrap"><?php echo e($log->details['boe'] ?? 'N/A'); ?></td>
                            <td class="px-6 py-4 whitespace-nowrap"><?php echo e($log->details['vehicle_number'] ?? 'N/A'); ?></td>
                            <td class="px-6 py-4 whitespace-nowrap"><?php echo e($log->details['destination'] ?? 'N/A'); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">No dispatch logs found</td>
                        </tr>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </tbody>
            </table>
        </div>
        
        <!--[if BLOCK]><![endif]--><?php if($dispatchLogs->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <?php echo e($dispatchLogs->links()); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\laragon\www\Crowdfunding1\inventory system 3\resources\views/filament/resources/data-entry-assignment-resource/pages/dispatch-report-modal.blade.php ENDPATH**/ ?>