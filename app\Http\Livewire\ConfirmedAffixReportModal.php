<?php

namespace App\Http\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;
use App\Models\ConfirmedAffixed;
use App\Models\AllocationPoint;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;
use Filament\Support\Concerns\InteractsWithPageActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;

class ConfirmedAffixReportModal extends Component implements HasForms
{
    use InteractsWithForms, InteractsWithPageActions;
    use WithPagination;
    
    /**
     * Whether the modal is visible
     *
     * @var bool
     */
    public $showModal = false;

    /**
     * The export URL for the report
     *
     * @var string
     */
    public $exportUrl = '#';

    /**
     * Whether the component is currently loading data
     *
     * @var bool
     */
    public $isLoading = false;
    public $filters = [
        'device_id' => '',
        'boe' => '',
        'vehicle_number' => '',
        'start_date' => '',
        'start_time' => '',
        'end_date' => '',
        'end_time' => '',
        'status' => '',
        'destination' => '',
        'allocation_point_id' => '',
    ];

    protected $queryString = [
        'filters' => ['except' => ['']]
    ];

    public function mount()
    {
        $this->resetPage();
        $this->filters = array_merge([
            'device_id' => '',
            'boe' => '',
            'vehicle_number' => '',
            'start_date' => '',
            'start_time' => '',
            'end_date' => '',
            'end_time' => '',
            'status' => '',
            'destination' => '',
            'allocation_point_id' => '',
        ], $this->filters);
        
        $this->loadData();
    }
    
    public function updatingFilters()
    {
        $this->resetPage();
        $this->loadData();
    }
    
    protected function updateExportUrl()
    {
        try {
            $params = array_merge(
                ['filters' => $this->filters],
                ['_token' => csrf_token()]
            );
            
            $this->exportUrl = URL::temporarySignedRoute(
                'export.confirmed-affix-report',
                now()->addMinutes(30),
                $params
            );
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error generating export URL', [
                'error' => $e->getMessage(),
                'filters' => $this->filters
            ]);
            $this->exportUrl = '#';
        }
    }
    
    public function resetFilters()
    {
        $this->reset('filters');
        $this->resetPage();
    }
    
    public function getFilteredQuery()
    {
        $query = ConfirmedAffixed::query()
            ->with(['device', 'allocationPoint'])
            ->latest();
            
        // Apply filters
        if (!empty($this->filters['device_id'])) {
            $query->whereHas('device', function($q) {
                $q->where('device_id', 'like', '%' . $this->filters['device_id'] . '%');
            });
        }
        
        if (!empty($this->filters['boe'])) {
            $query->where('boe_sad_number', 'like', '%' . $this->filters['boe'] . '%');
        }
        
        if (!empty($this->filters['vehicle_number'])) {
            $query->where('vehicle_number', 'like', '%' . $this->filters['vehicle_number'] . '%');
        }
        
        if (!empty($this->filters['start_date'])) {
            $startDate = $this->filters['start_date'];
            if (!empty($this->filters['start_time'])) {
                $startDate .= ' ' . $this->filters['start_time'];
            }
            $query->where('created_at', '>=', $startDate);
        }
        
        if (!empty($this->filters['end_date'])) {
            $endDate = $this->filters['end_date'];
            if (!empty($this->filters['end_time'])) {
                $endDate .= ' ' . $this->filters['end_time'];
            } else {
                $endDate .= ' 23:59:59';
            }
            $query->where('created_at', '<=', $endDate);
        }
        
        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }
        
        if (!empty($this->filters['destination'])) {
            $query->where('destination', 'like', '%' . $this->filters['destination'] . '%');
        }
        
        if (!empty($this->filters['allocation_point_id'])) {
            $query->where('allocation_point_id', $this->filters['allocation_point_id']);
        }
        
        // Apply role-based filtering
        $user = Auth::user();
        if ($user) {
            if ($user->hasRole('allocation_point_incharge') && $user->allocation_point_id) {
                $query->where('allocation_point_id', $user->allocation_point_id);
            } elseif ($user->hasRole('destination_incharge') && $user->destination) {
                $query->where('destination', $user->destination);
            }
        }
        
        return $query;
    }
    
    /**
     * Get the export URL
     * 
     * @return string
     */
    public function getExportUrlProperty()
    {
        return $this->exportUrl;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\View\View|\Closure|string
     */
    /**
     * Mount the component
     *
     * @return void
     */
    /**
     * Mount the component
     *
     * @param array $filters
     * @return void
     */
    public function mount(array $filters = [])
    {
        $this->isLoading = false;
        
        // Initialize filters with default values if not set
        $defaultFilters = [
            'search' => '',
            'allocation_point_id' => null,
            'start_date' => now()->subDays(30)->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
            'status' => ''
        ];
        
        // Only merge filters that exist in our default filters
        $validFilters = array_intersect_key($filters, $defaultFilters);
        $this->filters = array_merge($defaultFilters, $validFilters);
    }

    protected function loadData()
    {
        $this->isLoading = true;
        
        try {
            // Update export URL with current filters
            $this->updateExportUrl();
            
            // Get the filtered query
            $query = $this->getFilteredQuery();
            
            // Get the paginated results
            $confirmedAffixes = $query->paginate(10);
            
            // Get allocation points based on user role
            $user = Auth::user();
            $allocationPointsQuery = AllocationPoint::query()->orderBy('name');
            
            if ($user && $user->hasRole('allocation_point_incharge') && $user->allocation_point_id) {
                $allocationPointsQuery->where('id', $user->allocation_point_id);
            }
            
            $allocationPoints = $allocationPointsQuery->get();
            
            // Log successful data loading
            \Illuminate\Support\Facades\Log::debug('Loading data for ConfirmedAffixReportModal', [
                'filters' => $this->filters,
                'record_count' => $confirmedAffixes->total(),
                'allocation_points_count' => $allocationPoints->count()
            ]);
            
            return [
                'confirmedAffixes' => $confirmedAffixes,
                'allocationPoints' => $allocationPoints,
                'exportUrl' => $this->exportUrl,
                'isLoading' => false
            ];
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in ConfirmedAffixReportModal@loadData', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'confirmedAffixes' => collect([]),
                'allocationPoints' => collect([]),
                'exportUrl' => '#',
                'isLoading' => false
            ];
        }
    }

    /**
     * Render the component
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        // Initialize default values as collections
        $viewData = [
            'filters' => $this->filters,
            'isLoading' => $this->isLoading,
            'showModal' => $this->showModal,
            'confirmedAffixes' => collect([]),
            'allocationPoints' => collect([]),
            'exportUrl' => $this->exportUrl
        ];
        
        try {
            // Only load data if the modal is shown
            if ($this->showModal) {
                $this->isLoading = true;
                
                // Load the data
                $data = $this->loadData();
                
                // Merge with defaults, ensuring all required keys exist
                $viewData = array_merge($viewData, [
                    'confirmedAffixes' => collect($data['confirmedAffixes'] ?? []),
                    'allocationPoints' => collect($data['allocationPoints'] ?? []),
                    'exportUrl' => $data['exportUrl'] ?? '#',
                ]);
                
                $this->isLoading = false;
            }
        } catch (\Exception $e) {
            $this->isLoading = false;
            \Illuminate\Support\Facades\Log::error('Error in render method', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        return view('livewire.confirmed-affix-report-modal', $viewData);
    }
    
    /**
     * Show the modal and load data
     *
     * @return void
     */
    public function showModal()
    {
        $this->showModal = true;
        $this->isLoading = true;
        $this->resetPage(); // Reset pagination when showing modal
        
        // Force a re-render to show loading state
        $this->dispatchBrowserEvent('show-loading');
    }
    
    /**
     * Close the modal and reset the component
     *
     * @return void
     */
    public function closeModal()
    {
        $this->showModal = false;
        $this->resetErrorBag();
        $this->resetValidation();
    }

    protected function applyRoleBasedFilters($query)
    {
        $user = auth()->user();
        
        // If user has allocation point permissions, apply them
        if ($user->can('view_allocation_points')) {
            $permissionPointIds = [];
            
            foreach ($user->permissions as $permission) {
                if (str_starts_with($permission->name, 'view_allocationpoint_')) {
                    $pointSlug = str_replace('view_allocationpoint_', '', $permission->name);
                    $point = \App\Models\AllocationPoint::where('slug', $pointSlug)->first();
                    if ($point) {
                        $permissionPointIds[] = $point->id;
                    }
                }
            }
            
            if (!empty($permissionPointIds)) {
                $query->whereIn('allocation_point_id', $permissionPointIds);
            }
        }
        
        // Apply any additional role-based filtering from the resource
        if (method_exists(\App\Filament\Resources\ConfirmedAffixedResource::class, 'getTableQuery')) {
            $resource = new \App\Filament\Resources\ConfirmedAffixedResource(new \App\Models\ConfirmedAffixed());
            $query = $resource->getTableQuery($query->getQuery());
        }
        
        return $query;
    }

    public function openModal()
    {
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetPage();
    }
}
