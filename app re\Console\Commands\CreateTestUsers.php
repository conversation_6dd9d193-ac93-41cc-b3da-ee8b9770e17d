<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class CreateTestUsers extends Command
{
    protected $signature = 'users:create-test';
    protected $description = 'Create test users with different roles';

    public function handle()
    {
        $this->createUser('Warehouse Manager', '<EMAIL>', 'Warehouse Manager');
        $this->createUser('Distribution Officer', '<EMAIL>', 'Distribution Officer');
        $this->createUser('Allocation Officer', '<EMAIL>', 'Allocation Officer');
        $this->createUser('Data Entry Officer', '<EMAIL>', 'Data Entry Officer');
        $this->createUser('Affixing Officer', '<EMAIL>', 'Affixing Officer');
        $this->createUser('Retrieval Officer', '<EMAIL>', 'Retrieval Officer');
        $this->createUser('Monitoring Officer', '<EMAIL>', 'Monitoring Officer');

        $this->info('Test users created successfully.');
    }

    private function createUser($name, $email, $roleName)
    {
        $user = User::create([
            'name' => $name,
            'email' => $email,
            'password' => bcrypt('password'),
        ]);

        $role = Role::firstOrCreate(['name' => $roleName]);
        $user->assignRole($role);

        $this->info("Created user: $name with role: $roleName");
    }
}
