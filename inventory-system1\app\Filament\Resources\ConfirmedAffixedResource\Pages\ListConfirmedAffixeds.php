<?php

namespace App\Filament\Resources\ConfirmedAffixedResource\Pages;

use App\Filament\Resources\ConfirmedAffixedResource;
use App\Models\Destination;
use App\Models\DeviceRetrieval;
use Filament\Pages\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;

class ListConfirmedAffixeds extends ListRecords
{
    protected static string $resource = ConfirmedAffixedResource::class;
    
    protected static string $view = 'filament.resources.confirmed-affixed-resource.pages.list-confirmed-affixeds';

    protected function getActions(): array
    {
        return [
            // Create button removed as records should only be created through data entry
        ];
    }

    protected function getTableFilters(): array
    {
        return [
            SelectFilter::make('destination')
                ->options(fn () => Destination::pluck('name', 'name')->toArray())
                ->label('Destination'),
            SelectFilter::make('destination_id')
                ->relationship('destination', 'name')
                ->label('Destination (By ID)'),
        ];
    }

    protected function getTableBulkActions(): array
    {
        return [
            BulkAction::make('bulkPickForAffixing')
                ->label('Pick Selected for Affixing')
                ->color('success')
                ->icon('heroicon-o-check-circle')
                ->requiresConfirmation()
                ->form([
                    \Filament\Forms\Components\DateTimePicker::make('affixing_date')
                        ->label('Affixing Date')
                        ->required()
                        ->default(now())
                ])
                ->action(function (Collection $records, array $data): void {
                    try {
                        DB::beginTransaction();

                        foreach ($records as $record) {
                            // Skip if already affixed
                            if ($record->status === 'AFFIXED') {
                                continue;
                            }

                            // Create device retrieval record
                            DeviceRetrieval::create([
                                'date' => now(),
                                'device_id' => $record->device_id,
                                'boe' => $record->boe,
                                'vehicle_number' => $record->vehicle_number,
                                'regime' => $record->regime,
                                'destination' => $record->destination,
                                'route_id' => $record->route_id,
                                'long_route_id' => $record->long_route_id,
                                'manifest_date' => $record->manifest_date,
                                'agency' => $record->agency,
                                'agent_contact' => $record->agent_contact,
                                'truck_number' => $record->truck_number,
                                'driver_name' => $record->driver_name,
                                'affixing_date' => $data['affixing_date'],
                                'retrieval_status' => 'NOT_RETRIEVED',
                                'transfer_status' => 'pending'
                            ]);

                            // Delete from assign_to_agents
                            DB::table('assign_to_agents')
                                ->where('device_id', $record->device_id)
                                ->delete();

                            // Delete the confirmed affixed record
                            $record->delete();
                        }

                        DB::commit();

                        Notification::make()
                            ->success()
                            ->title('Selected devices picked for affixing successfully')
                            ->send();

                    } catch (\Exception $e) {
                        DB::rollBack();
                        
                        Notification::make()
                            ->danger()
                            ->title('Error processing bulk affixing')
                            ->body($e->getMessage())
                            ->send();
                    }
                })
        ];
    }
}
